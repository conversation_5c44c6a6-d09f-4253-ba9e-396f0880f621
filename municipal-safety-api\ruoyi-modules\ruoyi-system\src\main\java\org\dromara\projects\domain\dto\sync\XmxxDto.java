package org.dromara.projects.domain.dto.sync;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目信息数据传输对象
 * 对应外部API返回的XMXX数据结构
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class XmxxDto {

    /**
     * 项目所在省
     */
    private String XMSZS;

    /**
     * 项目所在区
     */
    private String XMSZQ;

    /**
     * 项目代码
     */
    private String PROJECTCODE;

    /**
     * 建设许可证编号
     */
    private String BUILDERLICENCENUM;

    /**
     * 项目名称
     */
    private String PROJECTNAME;

    /**
     * 地址
     */
    private String ADDRESS;

    /**
     * 造价
     */
    private BigDecimal COST;

    /**
     * 结构类型编号
     */
    private String STRUCTURETYPENUM;

    /**
     * 项目功能编号
     */
    private String PRJFUNCTIONNUM;

    /**
     * 投资类型
     */
    private String INVESTTYPE;

    /**
     * 基础类型
     */
    private String BASETYPE;

    /**
     * 面积
     */
    private BigDecimal AREA;

    /**
     * 开始日期
     */
    private String BEGINDATE;

    /**
     * 结束日期
     */
    private String ENDDATE;

    /**
     * 监督进度
     */
    private String SUPERVISEPROGRESS;

    /**
     * 通知日期
     */
    private String INFORMDATE;

    /**
     * 申报描述
     */
    private String DECLAREDESCRIBE;

    /**
     * 监督机关
     */
    private String SUPERVISEORGAN;

    /**
     * 监督机关统一社会信用代码
     */
    private String SUPERVISEORGANSOCIALCREDITCODE;

    /**
     * 区域代码
     */
    private String AREACODE;

    /**
     * 监督用户
     */
    private String SUPERVISEUSER;

    /**
     * 位置X坐标
     */
    private String LOCATIONX;

    /**
     * 位置Y坐标
     */
    private String LOCATIONY;
}
