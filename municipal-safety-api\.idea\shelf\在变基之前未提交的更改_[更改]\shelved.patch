Index: ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package org.dromara.system.service.impl;\r\n\r\nimport cn.hutool.core.bean.BeanUtil;\r\nimport cn.hutool.core.collection.CollUtil;\r\nimport cn.hutool.core.convert.Convert;\r\nimport cn.hutool.core.lang.tree.Tree;\r\nimport cn.hutool.core.util.ObjectUtil;\r\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\r\nimport com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;\r\nimport com.baomidou.mybatisplus.core.toolkit.Wrappers;\r\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\r\nimport lombok.RequiredArgsConstructor;\r\nimport org.dromara.common.core.constant.CacheNames;\r\nimport org.dromara.common.core.constant.SystemConstants;\r\nimport org.dromara.common.core.domain.dto.DeptDTO;\r\nimport org.dromara.common.core.exception.ServiceException;\r\nimport org.dromara.common.core.service.DeptService;\r\nimport org.dromara.common.core.utils.*;\r\nimport org.dromara.common.mybatis.core.page.PageQuery;\r\nimport org.dromara.common.mybatis.core.page.TableDataInfo;\r\nimport org.dromara.common.mybatis.helper.DataBaseHelper;\r\nimport org.dromara.common.redis.utils.CacheUtils;\r\nimport org.dromara.common.satoken.utils.LoginHelper;\r\nimport org.dromara.system.domain.SysDept;\r\nimport org.dromara.system.domain.SysRole;\r\nimport org.dromara.system.domain.SysUser;\r\nimport org.dromara.system.domain.bo.SysDeptBo;\r\nimport org.dromara.system.domain.vo.SysDeptVo;\r\nimport org.dromara.system.mapper.SysDeptMapper;\r\nimport org.dromara.system.mapper.SysRoleMapper;\r\nimport org.dromara.system.mapper.SysUserMapper;\r\nimport org.dromara.system.service.ISysDeptService;\r\nimport org.springframework.cache.annotation.CacheEvict;\r\nimport org.springframework.cache.annotation.Cacheable;\r\nimport org.springframework.cache.annotation.Caching;\r\nimport org.springframework.stereotype.Service;\r\nimport org.springframework.transaction.annotation.Transactional;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\n\r\n/**\r\n * 部门管理 服务实现\r\n *\r\n * <AUTHOR> Li\r\n */\r\n@RequiredArgsConstructor\r\n@Service\r\npublic class SysDeptServiceImpl implements ISysDeptService, DeptService {\r\n\r\n    private final SysDeptMapper baseMapper;\r\n    private final SysRoleMapper roleMapper;\r\n    private final SysUserMapper userMapper;\r\n\r\n    /**\r\n     * 分页查询部门管理数据\r\n     *\r\n     * @param dept      部门信息\r\n     * @param pageQuery 分页对象\r\n     * @return 部门信息集合\r\n     */\r\n    @Override\r\n    public TableDataInfo<SysDeptVo> selectPageDeptList(SysDeptBo dept, PageQuery pageQuery) {\r\n        Page<SysDeptVo> page = baseMapper.selectPageDeptList(pageQuery.build(), buildQueryWrapper(dept));\r\n        return TableDataInfo.build(page);\r\n    }\r\n\r\n    /**\r\n     * 查询部门管理数据\r\n     *\r\n     * @param dept 部门信息\r\n     * @return 部门信息集合\r\n     */\r\n    @Override\r\n    public List<SysDeptVo> selectDeptList(SysDeptBo dept) {\r\n        LambdaQueryWrapper<SysDept> lqw = buildQueryWrapper(dept);\r\n        return baseMapper.selectDeptList(lqw);\r\n    }\r\n\r\n    /**\r\n     * 查询部门树结构信息\r\n     *\r\n     * @param bo 部门信息\r\n     * @return 部门树信息集合\r\n     */\r\n    @Override\r\n    public List<Tree<Long>> selectDeptTreeList(SysDeptBo bo) {\r\n        LambdaQueryWrapper<SysDept> lqw = buildQueryWrapper(bo);\r\n        List<SysDeptVo> depts = baseMapper.selectDeptList(lqw);\r\n        return buildDeptTreeSelect(depts);\r\n    }\r\n\r\n    private LambdaQueryWrapper<SysDept> buildQueryWrapper(SysDeptBo bo) {\r\n        LambdaQueryWrapper<SysDept> lqw = Wrappers.lambdaQuery();\r\n        lqw.eq(SysDept::getDelFlag, SystemConstants.NORMAL);\r\n        lqw.eq(ObjectUtil.isNotNull(bo.getDeptId()), SysDept::getDeptId, bo.getDeptId());\r\n        lqw.eq(ObjectUtil.isNotNull(bo.getParentId()), SysDept::getParentId, bo.getParentId());\r\n        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), SysDept::getDeptName, bo.getDeptName());\r\n        lqw.like(StringUtils.isNotBlank(bo.getDeptCategory()), SysDept::getDeptCategory, bo.getDeptCategory());\r\n        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysDept::getStatus, bo.getStatus());\r\n        lqw.eq(StringUtils.isNotBlank(bo.getProvinceCode()), SysDept::getProvinceCode, bo.getProvinceCode());\r\n        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), SysDept::getCityCode, bo.getCityCode());\r\n        lqw.eq(StringUtils.isNotBlank(bo.getDistrictCode()), SysDept::getDistrictCode, bo.getDistrictCode());\r\n        if (StringUtils.equals(bo.getDeptType(), \"GOV_QS\")) {\r\n            lqw.in(SysDept::getDeptType, \"GOV_CITY_QS\", \"GOV_DISTRICT_QS\");\r\n        } else {\r\n            lqw.eq(StringUtils.isNotBlank(bo.getDeptType()), SysDept::getDeptType, bo.getDeptType());\r\n        }\r\n        lqw.orderByAsc(SysDept::getAncestors);\r\n        lqw.orderByAsc(SysDept::getParentId);\r\n        lqw.orderByAsc(SysDept::getOrderNum);\r\n        lqw.orderByAsc(SysDept::getDeptId);\r\n        if (ObjectUtil.isNotNull(bo.getBelongDeptId())) {\r\n            //部门树搜索\r\n            lqw.and(x -> {\r\n                Long parentId = bo.getBelongDeptId();\r\n                List<SysDept> deptList = baseMapper.selectListByParentId(parentId);\r\n                List<Long> deptIds = StreamUtils.toList(deptList, SysDept::getDeptId);\r\n                deptIds.add(parentId);\r\n                x.in(SysDept::getDeptId, deptIds);\r\n            });\r\n        }\r\n        return lqw;\r\n    }\r\n\r\n    /**\r\n     * 构建前端所需要下拉树结构\r\n     *\r\n     * @param depts 部门列表\r\n     * @return 下拉树结构列表\r\n     */\r\n    @Override\r\n    public List<Tree<Long>> buildDeptTreeSelect(List<SysDeptVo> depts) {\r\n        if (CollUtil.isEmpty(depts)) {\r\n            return CollUtil.newArrayList();\r\n        }\r\n        // 获取当前列表中每一个节点的parentId，然后在列表中查找是否有id与其parentId对应，若无对应，则表明此时节点列表中，该节点在当前列表中属于顶级节点\r\n        List<Tree<Long>> treeList = CollUtil.newArrayList();\r\n        for (SysDeptVo d : depts) {\r\n            Long parentId = d.getParentId();\r\n            SysDeptVo sysDeptVo = StreamUtils.findFirst(depts, it -> it.getDeptId().longValue() == parentId);\r\n            if (ObjectUtil.isNull(sysDeptVo)) {\r\n                List<Tree<Long>> trees = TreeBuildUtils.build(depts, parentId, (dept, tree) ->\r\n                    tree.setId(dept.getDeptId())\r\n                        .setParentId(dept.getParentId())\r\n                        .setName(dept.getDeptName())\r\n                        .setWeight(dept.getOrderNum())\r\n                        .putExtra(\"disabled\", SystemConstants.DISABLE.equals(dept.getStatus())));\r\n                Tree<Long> tree = StreamUtils.findFirst(trees, it -> it.getId().longValue() == d.getDeptId());\r\n                treeList.add(tree);\r\n            }\r\n        }\r\n        return treeList;\r\n    }\r\n\r\n    /**\r\n     * 根据角色ID查询部门树信息\r\n     *\r\n     * @param roleId 角色ID\r\n     * @return 选中部门列表\r\n     */\r\n    @Override\r\n    public List<Long> selectDeptListByRoleId(Long roleId) {\r\n        SysRole role = roleMapper.selectById(roleId);\r\n        return baseMapper.selectDeptListByRoleId(roleId, role.getDeptCheckStrictly());\r\n    }\r\n\r\n    /**\r\n     * 根据部门ID查询信息\r\n     *\r\n     * @param deptId 部门ID\r\n     * @return 部门信息\r\n     */\r\n    @Cacheable(cacheNames = CacheNames.SYS_DEPT, key = \"#deptId\")\r\n    @Override\r\n    public SysDeptVo selectDeptById(Long deptId) {\r\n        SysDeptVo dept = baseMapper.selectVoById(deptId);\r\n        if (ObjectUtil.isNull(dept)) {\r\n            return null;\r\n        }\r\n        SysDeptVo parentDept = baseMapper.selectVoOne(new LambdaQueryWrapper<SysDept>()\r\n            .select(SysDept::getDeptName).eq(SysDept::getDeptId, dept.getParentId()));\r\n        dept.setParentName(ObjectUtils.notNullGetter(parentDept, SysDeptVo::getDeptName));\r\n        return dept;\r\n    }\r\n\r\n    @Override\r\n    public List<SysDeptVo> selectDeptByIds(List<Long> deptIds) {\r\n        return baseMapper.selectDeptList(new LambdaQueryWrapper<SysDept>()\r\n            .select(SysDept::getDeptId, SysDept::getDeptName, SysDept::getLeader)\r\n            .eq(SysDept::getStatus, SystemConstants.NORMAL)\r\n            .in(CollUtil.isNotEmpty(deptIds), SysDept::getDeptId, deptIds));\r\n    }\r\n\r\n    /**\r\n     * 通过部门ID查询部门名称\r\n     *\r\n     * @param deptIds 部门ID串逗号分隔\r\n     * @return 部门名称串逗号分隔\r\n     */\r\n    @Override\r\n    public String selectDeptNameByIds(String deptIds) {\r\n        List<String> list = new ArrayList<>();\r\n        for (Long id : StringUtils.splitTo(deptIds, Convert::toLong)) {\r\n            SysDeptVo vo = SpringUtils.getAopProxy(this).selectDeptById(id);\r\n            if (ObjectUtil.isNotNull(vo)) {\r\n                list.add(vo.getDeptName());\r\n            }\r\n        }\r\n        return String.join(StringUtils.SEPARATOR, list);\r\n    }\r\n\r\n    /**\r\n     * 根据部门ID查询部门负责人\r\n     *\r\n     * @param deptId 部门ID，用于指定需要查询的部门\r\n     * @return 返回该部门的负责人ID\r\n     */\r\n    @Override\r\n    public Long selectDeptLeaderById(Long deptId) {\r\n        SysDeptVo vo = SpringUtils.getAopProxy(this).selectDeptById(deptId);\r\n        return vo.getLeader();\r\n    }\r\n\r\n    /**\r\n     * 查询部门\r\n     *\r\n     * @return 部门列表\r\n     */\r\n    @Override\r\n    public List<DeptDTO> selectDeptsByList() {\r\n        List<SysDeptVo> list = baseMapper.selectDeptList(new LambdaQueryWrapper<SysDept>()\r\n            .select(SysDept::getDeptId, SysDept::getDeptName, SysDept::getParentId)\r\n            .eq(SysDept::getStatus, SystemConstants.NORMAL));\r\n        return BeanUtil.copyToList(list, DeptDTO.class);\r\n    }\r\n\r\n    /**\r\n     * 根据ID查询所有子部门数（正常状态）\r\n     *\r\n     * @param deptId 部门ID\r\n     * @return 子部门数\r\n     */\r\n    @Override\r\n    public long selectNormalChildrenDeptById(Long deptId) {\r\n        return baseMapper.selectCount(new LambdaQueryWrapper<SysDept>()\r\n            .eq(SysDept::getStatus, SystemConstants.NORMAL)\r\n            .apply(DataBaseHelper.findInSet(deptId, \"ancestors\")));\r\n    }\r\n\r\n    /**\r\n     * 是否存在子节点\r\n     *\r\n     * @param deptId 部门ID\r\n     * @return 结果\r\n     */\r\n    @Override\r\n    public boolean hasChildByDeptId(Long deptId) {\r\n        return baseMapper.exists(new LambdaQueryWrapper<SysDept>()\r\n            .eq(SysDept::getParentId, deptId));\r\n    }\r\n\r\n    /**\r\n     * 查询部门是否存在用户\r\n     *\r\n     * @param deptId 部门ID\r\n     * @return 结果 true 存在 false 不存在\r\n     */\r\n    @Override\r\n    public boolean checkDeptExistUser(Long deptId) {\r\n        return userMapper.exists(new LambdaQueryWrapper<SysUser>()\r\n            .eq(SysUser::getDeptId, deptId));\r\n    }\r\n\r\n    /**\r\n     * 校验部门名称是否唯一\r\n     *\r\n     * @param dept 部门信息\r\n     * @return 结果\r\n     */\r\n    @Override\r\n    public boolean checkDeptNameUnique(SysDeptBo dept) {\r\n        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysDept>()\r\n            .eq(SysDept::getDeptName, dept.getDeptName())\r\n            .eq(SysDept::getParentId, dept.getParentId())\r\n            .ne(ObjectUtil.isNotNull(dept.getDeptId()), SysDept::getDeptId, dept.getDeptId()));\r\n        return !exist;\r\n    }\r\n\r\n    /**\r\n     * 校验部门是否有数据权限\r\n     *\r\n     * @param deptId 部门id\r\n     */\r\n    @Override\r\n    public void checkDeptDataScope(Long deptId) {\r\n        if (ObjectUtil.isNull(deptId)) {\r\n            return;\r\n        }\r\n        if (LoginHelper.isSuperAdmin()) {\r\n            return;\r\n        }\r\n        if (baseMapper.countDeptById(deptId) == 0) {\r\n            throw new ServiceException(\"没有权限访问部门数据！\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 新增保存部门信息\r\n     *\r\n     * @param bo 部门信息\r\n     * @return 结果\r\n     */\r\n    @CacheEvict(cacheNames = CacheNames.SYS_DEPT_AND_CHILD, allEntries = true)\r\n    @Override\r\n    public int insertDept(SysDeptBo bo) {\r\n        SysDept info = baseMapper.selectById(bo.getParentId());\r\n        // 如果父节点不为正常状态,则不允许新增子节点\r\n        if (!SystemConstants.NORMAL.equals(info.getStatus())) {\r\n            throw new ServiceException(\"部门停用，不允许新增\");\r\n        }\r\n        SysDept dept = MapstructUtils.convert(bo, SysDept.class);\r\n        dept.setAncestors(info.getAncestors() + StringUtils.SEPARATOR + dept.getParentId());\r\n        return baseMapper.insert(dept);\r\n    }\r\n\r\n    /**\r\n     * 修改保存部门信息\r\n     *\r\n     * @param bo 部门信息\r\n     * @return 结果\r\n     */\r\n    @Caching(evict = {\r\n        @CacheEvict(cacheNames = CacheNames.SYS_DEPT, key = \"#bo.deptId\"),\r\n        @CacheEvict(cacheNames = CacheNames.SYS_DEPT_AND_CHILD, allEntries = true)\r\n    })\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public int updateDept(SysDeptBo bo) {\r\n        SysDept dept = MapstructUtils.convert(bo, SysDept.class);\r\n        SysDept oldDept = baseMapper.selectById(dept.getDeptId());\r\n        if (ObjectUtil.isNull(oldDept)) {\r\n            throw new ServiceException(\"部门不存在，无法修改\");\r\n        }\r\n        if (!oldDept.getParentId().equals(dept.getParentId())) {\r\n            // 如果是新父部门 则校验是否具有新父部门权限 避免越权\r\n            this.checkDeptDataScope(dept.getParentId());\r\n            SysDept newParentDept = baseMapper.selectById(dept.getParentId());\r\n            if (ObjectUtil.isNotNull(newParentDept)) {\r\n                String newAncestors = newParentDept.getAncestors() + StringUtils.SEPARATOR + newParentDept.getDeptId();\r\n                String oldAncestors = oldDept.getAncestors();\r\n                dept.setAncestors(newAncestors);\r\n                updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);\r\n            }\r\n        } else {\r\n            dept.setAncestors(oldDept.getAncestors());\r\n        }\r\n        int result = baseMapper.updateById(dept);\r\n        // 如果部门状态为启用，且部门祖级列表不为空，且部门祖级列表不等于根部门祖级列表（如果部门祖级列表不等于根部门祖级列表，则说明存在上级部门）\r\n        if (SystemConstants.NORMAL.equals(dept.getStatus())\r\n            && StringUtils.isNotEmpty(dept.getAncestors())\r\n            && !StringUtils.equals(SystemConstants.ROOT_DEPT_ANCESTORS, dept.getAncestors())) {\r\n            // 如果该部门是启用状态，则启用该部门的所有上级部门\r\n            updateParentDeptStatusNormal(dept);\r\n        }\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 修改该部门的父级部门状态\r\n     *\r\n     * @param dept 当前部门\r\n     */\r\n    private void updateParentDeptStatusNormal(SysDept dept) {\r\n        String ancestors = dept.getAncestors();\r\n        Long[] deptIds = Convert.toLongArray(ancestors);\r\n        baseMapper.update(null, new LambdaUpdateWrapper<SysDept>()\r\n            .set(SysDept::getStatus, SystemConstants.NORMAL)\r\n            .in(SysDept::getDeptId, Arrays.asList(deptIds)));\r\n    }\r\n\r\n    /**\r\n     * 修改子元素关系\r\n     *\r\n     * @param deptId       被修改的部门ID\r\n     * @param newAncestors 新的父ID集合\r\n     * @param oldAncestors 旧的父ID集合\r\n     */\r\n    private void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {\r\n        List<SysDept> children = baseMapper.selectList(new LambdaQueryWrapper<SysDept>()\r\n            .apply(DataBaseHelper.findInSet(deptId, \"ancestors\")));\r\n        List<SysDept> list = new ArrayList<>();\r\n        for (SysDept child : children) {\r\n            SysDept dept = new SysDept();\r\n            dept.setDeptId(child.getDeptId());\r\n            dept.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));\r\n            list.add(dept);\r\n        }\r\n        if (CollUtil.isNotEmpty(list)) {\r\n            if (baseMapper.updateBatchById(list)) {\r\n                list.forEach(dept -> CacheUtils.evict(CacheNames.SYS_DEPT, dept.getDeptId()));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 删除部门管理信息\r\n     *\r\n     * @param deptId 部门ID\r\n     * @return 结果\r\n     */\r\n    @Caching(evict = {\r\n        @CacheEvict(cacheNames = CacheNames.SYS_DEPT, key = \"#deptId\"),\r\n        @CacheEvict(cacheNames = CacheNames.SYS_DEPT_AND_CHILD, key = \"#deptId\")\r\n    })\r\n    @Override\r\n    public int deleteDeptById(Long deptId) {\r\n        return baseMapper.deleteById(deptId);\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java b/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java
--- a/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java	(revision d2ab2462389c0bb600afb5337007fc4c37365b42)
+++ b/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java	(date 1753772212793)
@@ -105,7 +105,7 @@
         if (StringUtils.equals(bo.getDeptType(), "GOV_QS")) {
             lqw.in(SysDept::getDeptType, "GOV_CITY_QS", "GOV_DISTRICT_QS");
         } else {
-            lqw.eq(StringUtils.isNotBlank(bo.getDeptType()), SysDept::getDeptType, bo.getDeptType());
+            lqw.like(StringUtils.isNotBlank(bo.getDeptType()), SysDept::getDeptType, bo.getDeptType());
         }
         lqw.orderByAsc(SysDept::getAncestors);
         lqw.orderByAsc(SysDept::getParentId);
Index: ruoyi-admin/src/main/resources/application-dev.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>--- # 监控中心配置\r\nspring.boot.admin.client:\r\n  # 增加客户端开关\r\n  enabled: true\r\n  url: http://localhost:9090/admin\r\n  instance:\r\n    service-host-type: IP\r\n    metadata:\r\n      username: ${spring.boot.admin.client.username}\r\n      userpassword: ${spring.boot.admin.client.password}\r\n  username: @monitor.username@\r\n  password: @monitor.password@\r\n\r\n--- # snail-job 配置\r\nsnail-job:\r\n  enabled: true\r\n  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务\r\n  group: \"ruoyi_group\"\r\n  # SnailJob 接入验证令牌 详见 script/sql/ry_job.sql `sj_group_config` 表\r\n  token: \"SJ_cKqBTPzCsWA3VyuCfFoccmuIEGXjr5KT\"\r\n  server:\r\n    host: 127.0.0.1\r\n    port: 17888\r\n  # 命名空间UUID 详见 script/sql/ry_job.sql `sj_namespace`表`unique_id`字段\r\n  namespace: ${spring.profiles.active}\r\n  # 随主应用端口漂移\r\n  port: 2${server.port}\r\n  # 客户端ip指定\r\n  host:\r\n  # RPC类型: netty, grpc\r\n  rpc-type: grpc\r\n\r\n--- # 数据源配置\r\nspring:\r\n  datasource:\r\n    type: com.zaxxer.hikari.HikariDataSource\r\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\r\n    dynamic:\r\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\r\n      p6spy: true\r\n      # 设置默认的数据源或者数据源组,默认值即为 master\r\n      primary: master\r\n      # 严格模式 匹配不到数据源则报错\r\n      strict: true\r\n      datasource:\r\n        # 主库数据源\r\n        master:\r\n          type: ${spring.datasource.type}\r\n          driverClassName: com.mysql.cj.jdbc.Driver\r\n          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\r\n          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)\r\n          url: ************************************************************************************************************************************************************************************************************************************************************************************************************************\r\n#          url: *******************************************************************************************************************************************************************************************************************************************************************************************************************\r\n          username: zt\r\n          password: Gsjtsz@20241120\r\n      #        # 从库数据源\r\n      #        slave:\r\n      #          lazy: true\r\n      #          type: ${spring.datasource.type}\r\n      #          driverClassName: com.mysql.cj.jdbc.Driver\r\n      #          url: **************************************************************************************************************************************************************************************************************************************************************      #          username:\r\n      #          password:\r\n      #        oracle:\r\n      #          type: ${spring.datasource.type}\r\n      #          driverClassName: oracle.jdbc.OracleDriver\r\n      #          url: *****************************************      #          username: ROOT\r\n      #          password: root\r\n      #        postgres:\r\n      #          type: ${spring.datasource.type}\r\n      #          driverClassName: org.postgresql.Driver\r\n      #          url: **********************************************************************************************************************************************      #          username: root\r\n      #          password: root\r\n      #        sqlserver:\r\n      #          type: ${spring.datasource.type}\r\n      #          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver\r\n      #          url: ***********************************************************************************************************************      #          username: SA\r\n      #          password: root\r\n      hikari:\r\n        # 最大连接池数量\r\n        maxPoolSize: 20\r\n        # 最小空闲线程数量\r\n        minIdle: 10\r\n        # 配置获取连接等待超时的时间\r\n        connectionTimeout: 30000\r\n        # 校验超时时间\r\n        validationTimeout: 5000\r\n        # 空闲连接存活最大时间，默认10分钟\r\n        idleTimeout: 600000\r\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\r\n        maxLifetime: 1800000\r\n        # 多久检查一次连接的活性\r\n        keepaliveTime: 30000\r\n\r\n--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)\r\nspring.data:\r\n  redis:\r\n    # 地址\r\n    host: localhost\r\n    # 端口，默认为6379\r\n    port: 6379\r\n    # 数据库索引\r\n    database: 2\r\n    # redis 密码必须配置,没有密码注释下方 password\r\n    #    password: ruoyi123\r\n    # 连接超时时间\r\n    timeout: 10s\r\n    # 是否开启ssl\r\n    ssl.enabled: false\r\n\r\n# redisson 配置\r\nredisson:\r\n  # redis key前缀\r\n  keyPrefix:\r\n  # 线程池数量\r\n  threads: 4\r\n  # Netty线程池数量\r\n  nettyThreads: 8\r\n  # 单节点配置\r\n  singleServerConfig:\r\n    # 客户端名称 不能用中文\r\n    clientName: RuoYi-Vue-Plus\r\n    # 最小空闲连接数\r\n    connectionMinimumIdleSize: 8\r\n    # 连接池大小\r\n    connectionPoolSize: 32\r\n    # 连接空闲超时，单位：毫秒\r\n    idleConnectionTimeout: 10000\r\n    # 命令等待超时，单位：毫秒\r\n    timeout: 3000\r\n    # 发布和订阅连接池大小\r\n    subscriptionConnectionPoolSize: 50\r\n\r\n--- # mail 邮件发送\r\nmail:\r\n  enabled: false\r\n  host: smtp.163.com\r\n  port: 465\r\n  # 是否需要用户名密码验证\r\n  auth: true\r\n  # 发送方，遵循RFC-822标准\r\n  from: <EMAIL>\r\n  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）\r\n  user: <EMAIL>\r\n  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）\r\n  pass: xxxxxxxxxx\r\n  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。\r\n  starttlsEnable: true\r\n  # 使用SSL安全连接\r\n  sslEnable: true\r\n  # SMTP超时时长，单位毫秒，缺省值不超时\r\n  timeout: 0\r\n  # Socket连接超时值，单位毫秒，缺省值不超时\r\n  connectionTimeout: 0\r\n\r\n--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商\r\n# https://sms4j.com/doc3/ 差异配置文档地址 支持单厂商多配置，可以配置多个同时使用\r\nsms:\r\n  # 配置源类型用于标定配置来源(interface,yaml)\r\n  config-type: yaml\r\n  # 用于标定yml中的配置是否开启短信拦截，接口配置不受此限制\r\n  restricted: true\r\n  # 短信拦截限制单手机号每分钟最大发送，只对开启了拦截的配置有效\r\n  minute-max: 1\r\n  # 短信拦截限制单手机号每日最大发送量，只对开启了拦截的配置有效\r\n  account-max: 30\r\n  # 以下配置来自于 org.dromara.sms4j.provider.config.BaseConfig类中\r\n  blends:\r\n    # 唯一ID 用于发送短信寻找具体配置 随便定义别用中文即可\r\n    # 可以同时存在两个相同厂商 例如: ali1 ali2 两个不同的阿里短信账号 也可用于区分租户\r\n    config1:\r\n      # 框架定义的厂商名称标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分\r\n      supplier: alibaba\r\n      # 有些称为accessKey有些称之为apiKey，也有称为sdkKey或者appId。\r\n      access-key-id: ${ali.AccessKeyId}\r\n      # 称为accessSecret有些称之为apiSecret\r\n      access-key-secret: ${ali.AccessKeySecret}\r\n      signature: 甘肃建投数字科技\r\n      template-id: SMS_483920387\r\n      templateName: code\r\n    config2:\r\n      # 厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分\r\n      supplier: tencent\r\n      access-key-id: 您的accessKey\r\n      access-key-secret: 您的accessKeySecret\r\n      signature: 您的短信签名\r\n      sdk-app-id: 您的sdkAppId\r\n\r\n\r\n--- # 三方授权\r\njustauth:\r\n  # 前端外网访问地址\r\n  address: http://localhost:80\r\n  type:\r\n    maxkey:\r\n      # maxkey 服务器地址\r\n      # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据\r\n      server-url: http://sso.maxkey.top\r\n      client-id: 876892492581044224\r\n      client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8\r\n      redirect-uri: ${justauth.address}/social-callback?source=maxkey\r\n    topiam:\r\n      # topiam 服务器地址\r\n      server-url: http://127.0.0.1:1898/api/v1/authorize/y0q************spq***********8ol\r\n      client-id: 449c4*********937************759\r\n      client-secret: ac7***********1e0************28d\r\n      redirect-uri: ${justauth.address}/social-callback?source=topiam\r\n      scopes: [ openid, email, phone, profile ]\r\n    qq:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=qq\r\n      union-id: false\r\n    weibo:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=weibo\r\n    gitee:\r\n      client-id: 91436b7940090d09c72c7daf85b959cfd5f215d67eea73acbf61b6b590751a98\r\n      client-secret: 02c6fcfd70342980cd8dd2f2c06c1a350645d76c754d7a264c4e125f9ba915ac\r\n      redirect-uri: ${justauth.address}/social-callback?source=gitee\r\n    dingtalk:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=dingtalk\r\n    baidu:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=baidu\r\n    csdn:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=csdn\r\n    coding:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=coding\r\n      coding-group-name: xx\r\n    oschina:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=oschina\r\n    alipay_wallet:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=alipay_wallet\r\n      alipay-public-key: MIIB**************DAQAB\r\n    wechat_open:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_open\r\n    wechat_mp:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_mp\r\n    wechat_enterprise:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_enterprise\r\n      agent-id: 1000002\r\n    gitlab:\r\n      client-id: 10**********6\r\n      client-secret: 1f7d08**********5b7**********29e\r\n      redirect-uri: ${justauth.address}/social-callback?source=gitlab\r\n\r\n# 第三方人脸设备对接Url\r\nface:\r\n  apiUrl: http://***************:8016\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/ruoyi-admin/src/main/resources/application-dev.yml b/ruoyi-admin/src/main/resources/application-dev.yml
--- a/ruoyi-admin/src/main/resources/application-dev.yml	(revision d2ab2462389c0bb600afb5337007fc4c37365b42)
+++ b/ruoyi-admin/src/main/resources/application-dev.yml	(date 1753865647092)
@@ -1,7 +1,7 @@
 --- # 监控中心配置
 spring.boot.admin.client:
   # 增加客户端开关
-  enabled: true
+  enabled: false
   url: http://localhost:9090/admin
   instance:
     service-host-type: IP
@@ -11,9 +11,9 @@
   username: @monitor.username@
   password: @monitor.password@
 
---- # snail-job 配置
+--- # snail-job 配置4810
 snail-job:
-  enabled: true
+  enabled: false
   # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
   group: "ruoyi_group"
   # SnailJob 接入验证令牌 详见 script/sql/ry_job.sql `sj_group_config` 表
@@ -49,7 +49,7 @@
           driverClassName: com.mysql.cj.jdbc.Driver
           # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
           # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
-          url: ************************************************************************************************************************************************************************************************************************************************************************************************************************
+          url: *******************************************************************************************************************************************************************************************************************************************************************************************************************
 #          url: *******************************************************************************************************************************************************************************************************************************************************************************************************************
           username: zt
           password: Gsjtsz@20241120
