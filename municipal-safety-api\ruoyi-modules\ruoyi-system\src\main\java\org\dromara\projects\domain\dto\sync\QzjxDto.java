package org.dromara.projects.domain.dto.sync;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 起重机械信息数据传输对象
 * 对应外部API返回的QZJX数据结构
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class QzjxDto {

    /**
     * 施工许可证编号
     */
    private String CONSTRUCTIONPERMITNUM;

    /**
     * 设备备案编号
     */
    private String SBBABH;

    /**
     * 设备类型
     */
    private String SBLX;

    /**
     * 设备名称
     */
    private String SBMC;

    /**
     * 规格型号
     */
    private String GGXH;

    /**
     * 生产厂商
     */
    private String SCCS;

    /**
     * 生产厂商统一社会信用代码
     */
    private String SCCSTYSHXYDM;

    /**
     * 出厂编号
     */
    private String CCBH;

    /**
     * 出厂日期
     */
    private String CCRQ;

    /**
     * 制造许可证号
     */
    private String ZZXKZH;

    /**
     * 使用年限
     */
    private Integer SYNX;

    /**
     * 产权单位
     */
    private String CQDW;

    /**
     * 产权单位统一社会信用代码
     */
    private String CQDWTYSHXYDM;

    /**
     * 产权单位地址
     */
    private String CQDWDZ;

    /**
     * 企业法人代表
     */
    private String QYFRDB;

    /**
     * 法人身份证号
     */
    private String FRSFZH;

    /**
     * 联系人
     */
    private String LXR;

    /**
     * 联系电话
     */
    private String LXDH;

    /**
     * 工作级别
     */
    private BigDecimal GZJG;

    /**
     * 工作年月
     */
    private String GZNY;

    /**
     * 设备备案机关
     */
    private String SBBAJG;

    /**
     * 发证机关统一社会代码
     */
    private String FZJGTYSHDM;

    /**
     * 机械所在地市
     */
    private String JXSZDS;

    /**
     * 机械所在区县
     */
    private String JXSZQX;

    /**
     * 机械区域
     */
    private String JXQY;

    /**
     * 额定起重量
     */
    private BigDecimal EDQZL;

    /**
     * 额定起重力矩
     */
    private BigDecimal EDQZLJ;

    /**
     * 塔式起重机起重臂
     */
    private BigDecimal TSQZJQZCB;

    /**
     * 最大工作幅度
     */
    private String ZDGZFD;

    /**
     * 塔式起重机最大幅度起重量
     */
    private BigDecimal TSQZJZDFDQZL;

    /**
     * 最大独立起升高度
     */
    private BigDecimal ZDDLQSGD;

    /**
     * 最大起升高度
     */
    private BigDecimal ZDQSGD;

    /**
     * 主要结构件唯一编号
     */
    private String ZYJGJWYBH;

    /**
     * 塔式起重机内爬最终高度
     */
    private BigDecimal TSQZJNAZZDGD;

    /**
     * 主要结构件规格
     */
    private String ZYJGJGG;

    /**
     * 塔式起重机基础尺寸
     */
    private String TAQZJJQJCS;

    /**
     * 塔式起重机标准节尺寸
     */
    private String TSQZJBZJCS;

    /**
     * 施工升降机运行类型
     */
    private String SGSJJYTLX;

    /**
     * 吊篮最大载重量
     */
    private String DDJZGL;

    /**
     * 额定提升速度
     */
    private String EDTSSD;

    /**
     * 防坠安全器型号
     */
    private String FZAQQXH;

    /**
     * 架空长度
     */
    private String JKCC;

    /**
     * 设备类别
     */
    private String SBLB;

    /**
     * 门式起重机跨度
     */
    private String MSQZJKD;
}
