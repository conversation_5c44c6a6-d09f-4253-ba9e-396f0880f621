package org.dromara.projects.domain.dto.sync;

import lombok.Data;

import java.util.List;

/**
 * 同步响应数据传输对象
 * 对应外部API返回的完整数据结构
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class SyncResponseDto {

    /**
     * 响应消息
     */
    private String Msg;

    /**
     * 响应数据
     */
    private SyncDataDto Data;

    /**
     * 是否成功
     */
    private Boolean Success;

    @Data
    public static class SyncDataDto {
        /**
         * 起重机械信息列表
         */
        private List<QzjxDto> QZJX;

        /**
         * 参建单位信息列表
         */
        private List<CjdwDto> CJDW;

        /**
         * 项目信息列表
         */
        private List<XmxxDto> XMXX;

        /**
         * 相关人员信息列表
         */
        private List<XgryDto> XGRY;
    }
}
