package org.dromara.projects.domain.dto.sync;

import lombok.Data;

/**
 * 参建单位信息数据传输对象
 * 对应外部API返回的CJDW数据结构
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CjdwDto {

    /**
     * 建设许可证编号
     */
    private String BUILDERLICENCENUM;

    /**
     * 企业类型
     */
    private String CORPTYPE;

    /**
     * 企业名称
     */
    private String CORPNAME;

    /**
     * 统一社会信用代码
     */
    private String SOCIALCREDITCODE;

    /**
     * 法人代表
     */
    private String LEGALMAN;

    /**
     * 企业资质代码
     */
    private String CORPQUALIFICATIONCODE;

    /**
     * 企业资质
     */
    private String CORPQUALIFICATION;
}
