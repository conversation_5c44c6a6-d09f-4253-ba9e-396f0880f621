package org.dromara.projects.domain.dto.sync;

import lombok.Data;

/**
 * 相关人员信息数据传输对象
 * 对应外部API返回的XGRY数据结构
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class XgryDto {

    /**
     * 记录编号
     */
    private String RECORDNUM;

    /**
     * 施工许可证编号
     */
    private String CONSTRUCTIONPERMITNUM;

    /**
     * 姓名
     */
    private String NAME;

    /**
     * 身份证号
     */
    private String IDENTITYCARD;

    /**
     * 企业名称
     */
    private String CORPNAME;

    /**
     * 企业代码
     */
    private String CORPCODE;

    /**
     * 联系电话
     */
    private String LXDH;

    /**
     * 类别分类
     */
    private String LBFL;

    /**
     * 人员类型
     */
    private String PERSONTYPE;

    /**
     * 证书编号
     */
    private String ZSBH;
}
