package org.dromara.projects.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.area.Area;
import org.dromara.common.core.utils.area.AreaUtils;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.service.ISysPersonService;
import org.dromara.projects.domain.bo.PrjPersonnelBo;
import org.dromara.projects.domain.bo.PrjProjectsBo;
import org.dromara.projects.domain.dto.sync.*;
import org.dromara.projects.domain.vo.PrjPersonnelVo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjPersonnelService;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.projects.service.IPrjProjectsSyncService;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysRoleMapper;
import org.dromara.system.service.ISysEnterpriseInfoService;
import org.dromara.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目同步Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PrjProjectsSyncServiceImpl implements IPrjProjectsSyncService {

    private static final String EXTERNAL_API_URL = "https://zhaj.zjt.gansu.gov.cn:10124/dataexchangeserver/GGBApi/getGCXMInfo";
    private static final String API_SECRET_KEY = "1211f5ea378f4f858ba796336e01ab78";
    private static final int TIMEOUT_MS = 20000;

    // 日志常量定义
    private static final String LOG_PREFIX = "[项目同步]";
    private static final String LOG_PERMIT_KEY = "施工许可证号";
    private static final String LOG_USER_KEY = "操作用户";
    private static final String LOG_OPERATION_KEY = "操作类型";
    private static final String LOG_STATUS_KEY = "操作状态";
    private static final String LOG_DURATION_KEY = "耗时";
    private static final String LOG_PROJECT_NAME_KEY = "项目名称";
    private static final String LOG_COMPANY_KEY = "企业名称";
    private static final String LOG_PERSON_KEY = "人员姓名";

    // 操作类型常量
    private static final String OP_SYNC_PROJECT = "同步项目";
    private static final String OP_VALIDATE_PERMIT = "验证许可证";
    private static final String OP_VALIDATE_PERMISSION = "验证权限";
    private static final String OP_FETCH_EXTERNAL_DATA = "获取外部数据";
    private static final String OP_PROCESS_PROJECT = "处理项目信息";
    private static final String OP_PROCESS_COMPANY = "处理企业信息";
    private static final String OP_PROCESS_PERSONNEL = "处理人员信息";
    private static final String OP_SAVE_PROJECT = "保存项目";

    // 状态常量
    private static final String STATUS_SUCCESS = "成功";
    private static final String STATUS_FAILED = "失败";
    private static final String STATUS_START = "开始";
    private static final String STATUS_COMPLETE = "完成";

    private final IPrjProjectsService prjProjectsService;
    private final ISysEnterpriseInfoService sysEnterpriseInfoService;
    private final ISysPersonService sysPersonService;
    private final IPrjPersonnelService prjPersonnelService;
    private final SysDeptMapper sysDeptMapper;
    private final SysRoleMapper roleMapper;
    private final ISysUserService userService;
    private final DictService dictService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<PrjProjectsVo> syncProjectByPermitNo(String constructionPermitNo) {
        try {
            // 1. 验证输入参数
            R<String> validationResult = validateConstructionPermitNo(constructionPermitNo);
            if (!R.isSuccess(validationResult)) {
                return R.fail(validationResult.getMsg());
            }

            // 2. 验证用户权限 - 确保施工方用户只能同步属于自己公司的项目
            R<String> userValidationResult = validateUserPermissionForSync(constructionPermitNo);
            if (!R.isSuccess(userValidationResult)) {
                return R.fail(userValidationResult.getMsg());
            }

            // 3. 获取外部API数据
            SyncResponseDto syncData = fetchExternalData(constructionPermitNo);
            if (syncData == null || !Boolean.TRUE.equals(syncData.getSuccess())) {
                String errorMsg = "获取外部数据失败";
                if (syncData != null && StrUtil.isNotBlank(syncData.getMsg())) {
                    errorMsg += "：" + syncData.getMsg();
                } else {
                    errorMsg += "：无响应数据或响应格式错误";
                }
                return R.fail(errorMsg);
            }

            // 4. 验证同步数据的完整性
            R<String> dataValidationResult = validateSyncData(syncData);
            if (!R.isSuccess(dataValidationResult)) {
                return R.fail(dataValidationResult.getMsg());
            }

            // 5. 处理同步数据并保存到数据库
            PrjProjectsVo projectVo = processSyncData(syncData, constructionPermitNo);
            if (projectVo == null) {
                return R.fail("未找到匹配的项目信息，请检查施工许可证编号是否正确");
            }

            log.info("项目同步成功：{} - {}", constructionPermitNo, projectVo.getProjectName());
            return R.ok("同步成功", projectVo);

        } catch (Exception e) {
            log.error("同步项目信息失败，施工许可证编号：{}", constructionPermitNo, e);
            String errorMsg = "同步失败";
            if (e.getMessage() != null) {
                errorMsg += "：" + e.getMessage();
            }
            return R.fail(errorMsg);
        }
    }

    /**
     * 验证施工许可证编号
     */
    private R<String> validateConstructionPermitNo(String constructionPermitNo) {
        if (StrUtil.isBlank(constructionPermitNo)) {
            return R.fail("施工许可证编号不能为空");
        }

        // 去除前后空格
        constructionPermitNo = constructionPermitNo.trim();

        // 基本格式验证
        if (constructionPermitNo.length() < 10) {
            return R.fail("施工许可证编号格式不正确，长度不能少于10位");
        }

        // 可以添加更多的格式验证规则
        if (!constructionPermitNo.matches("^[0-9A-Za-z]+$")) {
            return R.fail("施工许可证编号只能包含数字和字母");
        }

        return R.ok("验证通过");
    }

    /**
     * 验证用户权限 - 确保施工方用户只能同步属于自己公司的项目
     */
    private R<String> validateUserPermissionForSync(String constructionPermitNo) {
        try {
            // 1. 获取当前登录用户信息
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null) {
                return R.fail("用户未登录，无法进行项目同步");
            }

            // 2. 检查用户是否属于施工方
            if (LoginHelper.isSuperAdmin() && !isConstructionCompanyUser(loginUser)) {
                // 如果不是施工方用户，允许同步（可能是管理员或其他角色）
                log.info("用户 {} 不是施工方用户，允许同步项目：{}", loginUser.getUsername(), constructionPermitNo);
                return R.ok("权限验证通过");
            }

            // 3. 如果是施工方用户，需要验证项目归属
            R<String> projectOwnershipResult = validateProjectOwnership(loginUser, constructionPermitNo);
            if (!R.isSuccess(projectOwnershipResult)) {
                return projectOwnershipResult;
            }

            log.info("施工方用户 {} 权限验证通过，可以同步项目：{}", loginUser.getUsername(), constructionPermitNo);
            return R.ok("权限验证通过");

        } catch (Exception e) {
            log.error("验证用户权限失败，施工许可证编号：{}", constructionPermitNo, e);
            return R.fail("权限验证失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否属于施工方
     */
    private boolean isConstructionCompanyUser(LoginUser loginUser) {
        try {
            // 检查用户的部门类型是否为施工方
            if (loginUser.getDeptId() == null) {
                return false;
            }

            SysDept userDept = sysDeptMapper.selectById(loginUser.getDeptId());
            if (userDept == null) {
                return false;
            }

            // 检查部门类型是否包含 CONSTRUCTION（支持多类型，逗号分隔）
            String deptType = userDept.getDeptType();
            if (StrUtil.isBlank(deptType)) {
                return false;
            }

            return deptType.contains("CONSTRUCTION");

        } catch (Exception e) {
            log.error("检查用户是否属于施工方失败，用户ID：{}", loginUser.getUserId(), e);
            return false;
        }
    }

    /**
     * 验证项目归属 - 检查项目是否属于当前用户的施工公司
     */
    private R<String> validateProjectOwnership(LoginUser loginUser, String constructionPermitNo) {
        try {
            // 1. 查找现有项目
            PrjProjectsBo queryBo = new PrjProjectsBo();
            queryBo.setConstructionPermitNo(constructionPermitNo);
            List<PrjProjectsVo> existingProjects = prjProjectsService.queryList(queryBo);

            if (CollUtil.isNotEmpty(existingProjects)) {
                // 项目已存在，检查是否属于当前用户的施工公司
                PrjProjectsVo existingProject = existingProjects.get(0);
                if (existingProject.getConstructionOrgId() != null) {
                    if (!existingProject.getConstructionOrgId().equals(loginUser.getDeptId())) {
                        return R.fail("无权限同步此项目：该项目不属于您的施工公司");
                    }
                }
            } else {
                // 项目不存在，需要从外部API获取数据进行预验证
                R<String> preValidationResult = preValidateProjectOwnershipFromApi(loginUser, constructionPermitNo);
                if (!R.isSuccess(preValidationResult)) {
                    return preValidationResult;
                }
            }

            return R.ok("项目归属验证通过");

        } catch (Exception e) {
            log.error("验证项目归属失败，施工许可证编号：{}", constructionPermitNo, e);
            return R.fail("项目归属验证失败：" + e.getMessage());
        }
    }

    /**
     * 从外部API预验证项目归属
     */
    private R<String> preValidateProjectOwnershipFromApi(LoginUser loginUser, String constructionPermitNo) {
        try {
            // 获取用户部门的统一社会信用代码
            SysDept userDept = sysDeptMapper.selectById(loginUser.getDeptId());
            if (userDept == null || StrUtil.isBlank(userDept.getDeptCode())) {
                return R.fail("无法获取用户所属公司的统一社会信用代码");
            }

            String userCompanyCreditCode = userDept.getDeptCode();

            // 调用外部API获取项目数据
            SyncResponseDto syncData = fetchExternalData(constructionPermitNo);
            if (syncData == null || !Boolean.TRUE.equals(syncData.getSuccess()) || syncData.getData() == null) {
                return R.fail("无法从外部API获取项目数据进行权限验证");
            }

            // 检查参建单位中是否包含用户的施工公司
            List<CjdwDto> cjdwList = syncData.getData().getCJDW();
            if (CollUtil.isEmpty(cjdwList)) {
                return R.fail("项目数据中未找到参建单位信息");
            }

            boolean hasPermission = cjdwList.stream()
                .filter(cjdw -> constructionPermitNo.equals(cjdw.getBUILDERLICENCENUM()))
                .filter(cjdw -> "施工单位".equals(cjdw.getCORPTYPE()))
                .anyMatch(cjdw -> userCompanyCreditCode.equals(cjdw.getSOCIALCREDITCODE()));

            if (!hasPermission) {
                return R.fail("无权限同步此项目：您的公司不是该项目的施工单位");
            }

            return R.ok("API预验证通过");

        } catch (Exception e) {
            log.error("从外部API预验证项目归属失败，施工许可证编号：{}", constructionPermitNo, e);
            return R.fail("API预验证失败：" + e.getMessage());
        }
    }

    /**
     * 验证同步数据的完整性
     */
    private R<String> validateSyncData(SyncResponseDto syncData) {
        if (syncData.getData() == null) {
            return R.fail("同步数据为空");
        }

        SyncResponseDto.SyncDataDto data = syncData.getData();

        // 验证项目信息
        if (CollUtil.isEmpty(data.getXMXX())) {
            return R.fail("未找到项目基本信息");
        }

        // 验证参建单位信息
        if (CollUtil.isEmpty(data.getCJDW())) {
            log.warn("未找到参建单位信息");
        }

        return R.ok("数据验证通过");
    }

    @Override
    public SyncResponseDto fetchExternalData(String constructionPermitNo) {
        try {
            String url = EXTERNAL_API_URL + "?SGXKZH=" + constructionPermitNo;
            log.info("调用外部API：{}", url);

            String responseBody = HttpRequest.post(url)
                .header("ryxxSecretKey", API_SECRET_KEY)
                .timeout(TIMEOUT_MS)
                .execute()
                .body();

            log.info("外部API响应数据长度：{}", responseBody != null ? responseBody.length() : 0);

            if (StrUtil.isBlank(responseBody)) {
                throw new RuntimeException("外部API返回空数据，请检查网络连接或稍后重试");
            }

            // 尝试解析JSON响应
            SyncResponseDto syncResponse;
            try {
                syncResponse = JSONUtil.toBean(responseBody, SyncResponseDto.class);
            } catch (Exception jsonException) {
                log.error("解析API响应JSON失败，响应内容：{}", responseBody, jsonException);
                throw new RuntimeException("API响应格式错误，无法解析数据");
            }

            // 验证响应结构
            if (syncResponse == null) {
                throw new RuntimeException("API响应解析后为空");
            }

            if (!Boolean.TRUE.equals(syncResponse.getSuccess())) {
                String errorMsg = "API调用失败";
                if (StrUtil.isNotBlank(syncResponse.getMsg())) {
                    errorMsg += "：" + syncResponse.getMsg();
                }
                throw new RuntimeException(errorMsg);
            }

            return syncResponse;

        } catch (cn.hutool.http.HttpException httpException) {
            log.error("HTTP请求失败，施工许可证编号：{}", constructionPermitNo, httpException);
            throw new RuntimeException("网络请求失败，请检查网络连接或稍后重试");
        } catch (RuntimeException runtimeException) {
            // 重新抛出已知的运行时异常
            throw runtimeException;
        } catch (Exception e) {
            log.error("调用外部API失败，施工许可证编号：{}", constructionPermitNo, e);
            throw new RuntimeException("调用外部API失败：" + e.getMessage());
        }
    }

    @Override
    public PrjProjectsVo processSyncData(SyncResponseDto syncData, String constructionPermitNo) {
        if (syncData.getData() == null) {
            return null;
        }

        SyncResponseDto.SyncDataDto data = syncData.getData();

        // 1. 处理项目基本信息
        PrjProjectsVo projectVo = processProjectInfo(data.getXMXX(), constructionPermitNo);
        if (projectVo == null) {
            return null;
        }

        // 2. 处理并保存参建单位信息
        Map<String, Long> companyDeptIdMap = processAndSaveCompanyInfo(data.getCJDW(), constructionPermitNo);

        // 3. 更新项目的单位关联信息（设置部门ID）
        updateProjectCompanyReferences(projectVo, companyDeptIdMap);

        // 4. 处理人员信息
        Map<Long, String> personnelRoleMap = processPersonnelInfo(projectVo, data.getXGRY(), companyDeptIdMap);

        // 5. 处理设备信息
        processEquipmentInfo(projectVo, data.getQZJX());

        // 6. 保存或更新项目信息
        saveOrUpdateProject(projectVo);

        // 7. 绑定项目人员关联
        bindProjectPersonnelFromSync(projectVo.getProjectId(), personnelRoleMap);

        return projectVo;
    }

    /**
     * 处理项目基本信息
     */
    private PrjProjectsVo processProjectInfo(java.util.List<XmxxDto> xmxxList, String constructionPermitNo) {
        if (CollUtil.isEmpty(xmxxList)) {
            return null;
        }

        // 查找匹配的项目信息
        XmxxDto xmxx = xmxxList.stream()
            .filter(x -> constructionPermitNo.equals(x.getBUILDERLICENCENUM()))
            .findFirst()
            .orElse(null);

        if (xmxx == null) {
            return null;
        }

        PrjProjectsVo projectVo = new PrjProjectsVo();

        // 映射基本信息
        projectVo.setProjectName(xmxx.getPROJECTNAME());
        projectVo.setProjectCode(xmxx.getPROJECTCODE());
        projectVo.setConstructionPermitNo(constructionPermitNo);
        projectVo.setLocationDetail(xmxx.getADDRESS());
        projectVo.setProjectOverview(xmxx.getDECLAREDESCRIBE());

        // 处理面积和投资
        if (xmxx.getAREA() != null) {
            projectVo.setSiteArea(xmxx.getAREA());
        }
        if (xmxx.getCOST() != null) {
            projectVo.setBudgetTotal(xmxx.getCOST());
        }

        // 处理日期
        if (StrUtil.isNotBlank(xmxx.getBEGINDATE())) {
            projectVo.setStartDate(parseDate(xmxx.getBEGINDATE()));
        }
        if (StrUtil.isNotBlank(xmxx.getENDDATE())) {
            projectVo.setPlannedEndDate(parseDate(xmxx.getENDDATE()));
        }

        // 处理地理位置
        if (StrUtil.isNotBlank(xmxx.getLOCATIONX()) && StrUtil.isNotBlank(xmxx.getLOCATIONY())) {
            projectVo.setLola(xmxx.getLOCATIONX() + "," + xmxx.getLOCATIONY());
        }

        // 处理地区信息
        processAreaInfo(projectVo, xmxx.getXMSZS(), xmxx.getXMSZQ());

        return projectVo;
    }

    /**
     * 处理地区信息
     *
     * @param projectVo    项目对象
     * @param cityName     市名称（接口返回的XMSZS字段）
     * @param districtName 区名称（接口返回的XMSZQ字段）
     */
    private void processAreaInfo(PrjProjectsVo projectVo, String cityName, String districtName) {
        try {
            // 1. 设置省份信息为甘肃省
            projectVo.setProvinceCode("620000");
            projectVo.setProvinceName("甘肃省");

            // 2. 处理市级信息
            if (StrUtil.isNotBlank(cityName)) {
                projectVo.setCityName(cityName);

                // 根据市名称获取市编码
                String cityCode = getCityCodeByName(cityName);
                if (StrUtil.isNotBlank(cityCode)) {
                    projectVo.setCityCode(cityCode);
                    log.info("成功获取市编码：{} -> {}", cityName, cityCode);
                } else {
                    log.warn("未找到市编码，市名称：{}", cityName);
                }
            }

            // 3. 处理区县信息
            if (StrUtil.isNotBlank(districtName)) {
                projectVo.setDistrictName(districtName);

                // 根据区名称和市编码获取区编码
                String districtCode = getDistrictCodeByName(districtName, projectVo.getCityCode());
                if (StrUtil.isNotBlank(districtCode)) {
                    projectVo.setDistrictCode(districtCode);
                    log.info("成功获取区编码：{} -> {}", districtName, districtCode);
                } else {
                    log.warn("未找到区编码，区名称：{}，市编码：{}", districtName, projectVo.getCityCode());
                }
            }

        } catch (Exception e) {
            log.error("处理地区信息失败，市名称：{}，区名称：{}", cityName, districtName, e);
        }
    }

    /**
     * 根据市名称获取市编码
     *
     * @param cityName 市名称
     * @return 市编码
     */
    private String getCityCodeByName(String cityName) {
        try {
            if (StrUtil.isBlank(cityName)) {
                return null;
            }

            // 构建查找路径：甘肃省/市名称
            String searchPath = "甘肃省/" + cityName;
            Area cityArea = AreaUtils.parseArea(searchPath);

            if (cityArea != null) {
                return String.valueOf(cityArea.getId());
            }

            // 如果直接查找失败，尝试模糊匹配（去掉"市"字后缀）
            if (cityName.endsWith("市")) {
                String cityNameWithoutSuffix = cityName.substring(0, cityName.length() - 1);
                searchPath = "甘肃省/" + cityNameWithoutSuffix + "市";
                cityArea = AreaUtils.parseArea(searchPath);
                if (cityArea != null) {
                    return String.valueOf(cityArea.getId());
                }
            }

            return null;
        } catch (Exception e) {
            log.error("根据市名称获取市编码失败：{}", cityName, e);
            return null;
        }
    }

    /**
     * 根据区名称和市编码获取区编码
     *
     * @param districtName 区名称
     * @param cityCode     市编码
     * @return 区编码
     */
    private String getDistrictCodeByName(String districtName, String cityCode) {
        try {
            if (StrUtil.isBlank(districtName)) {
                return null;
            }

            // 如果有市编码，先获取市区域对象
            if (StrUtil.isNotBlank(cityCode)) {
                Area cityArea = AreaUtils.getArea(Integer.valueOf(cityCode));
                if (cityArea != null && CollUtil.isNotEmpty(cityArea.getChildren())) {
                    // 在市的子区域中查找匹配的区
                    for (Area district : cityArea.getChildren()) {
                        if (districtName.equals(district.getName())) {
                            return String.valueOf(district.getId());
                        }
                    }

                    // 如果直接匹配失败，尝试模糊匹配
                    for (Area district : cityArea.getChildren()) {
                        String districtAreaName = district.getName();
                        // 去掉常见后缀进行匹配
                        if ((districtName.endsWith("区") && districtAreaName.equals(districtName)) ||
                            (districtName.endsWith("县") && districtAreaName.equals(districtName)) ||
                            (districtAreaName.contains(districtName.replace("区", "").replace("县", "")))) {
                            return String.valueOf(district.getId());
                        }
                    }
                }
            }

            // 如果通过市编码查找失败，尝试通过完整路径查找
            String searchPath;
            if (StrUtil.isNotBlank(cityCode)) {
                Area cityArea = AreaUtils.getArea(Integer.valueOf(cityCode));
                if (cityArea != null) {
                    searchPath = "甘肃省/" + cityArea.getName() + "/" + districtName;
                } else {
                    searchPath = "甘肃省/" + districtName;
                }
            } else {
                searchPath = "甘肃省/" + districtName;
            }

            Area districtArea = AreaUtils.parseArea(searchPath);
            if (districtArea != null) {
                return String.valueOf(districtArea.getId());
            }

            return null;
        } catch (Exception e) {
            log.error("根据区名称获取区编码失败，区名称：{}，市编码：{}", districtName, cityCode, e);
            return null;
        }
    }

    /**
     * 处理并保存参建单位信息
     */
    private Map<String, Long> processAndSaveCompanyInfo(List<CjdwDto> cjdwList, String constructionPermitNo) {
        Map<String, Long> companyDeptIdMap = new java.util.HashMap<>();

        if (CollUtil.isEmpty(cjdwList)) {
            return companyDeptIdMap;
        }

        // 过滤出与当前项目相关的单位
        List<CjdwDto> relevantCompanies = cjdwList.stream()
            .filter(cjdw -> constructionPermitNo.equals(cjdw.getBUILDERLICENCENUM()))
            .toList();

        // 按统一社会信用代码分组，聚合同一企业的多个类型
        Map<String, List<CjdwDto>> companiesByCode = relevantCompanies.stream()
            .filter(cjdw -> StrUtil.isNotBlank(cjdw.getSOCIALCREDITCODE()) && StrUtil.isNotBlank(cjdw.getCORPNAME()))
            .collect(Collectors.groupingBy(CjdwDto::getSOCIALCREDITCODE));

        for (Map.Entry<String, List<CjdwDto>> entry : companiesByCode.entrySet()) {
            String creditCode = entry.getKey();
            List<CjdwDto> companyRecords = entry.getValue();

            try {
                // 聚合同一企业的多个类型
                CjdwDto aggregatedCompany = aggregateCompanyTypes(companyRecords);

                // 保存或更新企业信息，返回部门ID
                Long deptId = saveOrUpdateEnterpriseWithSync(aggregatedCompany);
                if (deptId != null) {
                    // 为每个企业类型建立映射关系（映射到部门ID）
                    for (CjdwDto record : companyRecords) {
                        if (StrUtil.isNotBlank(record.getCORPTYPE())) {
                            companyDeptIdMap.put(record.getCORPTYPE(), deptId);
                        }
                    }
                    log.info("保存企业信息成功：{} - {} (类型: {}, 部门ID: {})",
                        aggregatedCompany.getCORPNAME(), creditCode, aggregatedCompany.getCORPTYPE(), deptId);
                }
            } catch (Exception e) {
                log.error("保存企业信息失败：{} - {}", creditCode,
                    companyRecords.get(0).getCORPNAME(), e);
            }
        }

        return companyDeptIdMap;
    }

    /**
     * 聚合同一企业的多个类型记录
     */
    private CjdwDto aggregateCompanyTypes(List<CjdwDto> companyRecords) {
        if (CollUtil.isEmpty(companyRecords)) {
            return null;
        }

        // 使用第一条记录作为基础
        CjdwDto aggregated = new CjdwDto();
        CjdwDto first = companyRecords.get(0);

        // 复制基本信息
        aggregated.setBUILDERLICENCENUM(first.getBUILDERLICENCENUM());
        aggregated.setCORPNAME(first.getCORPNAME());
        aggregated.setSOCIALCREDITCODE(first.getSOCIALCREDITCODE());
        aggregated.setLEGALMAN(first.getLEGALMAN());
        aggregated.setCORPQUALIFICATIONCODE(first.getCORPQUALIFICATIONCODE());
        aggregated.setCORPQUALIFICATION(first.getCORPQUALIFICATION());

        // 聚合所有企业类型，去重并用逗号分隔
        Set<String> enterpriseTypes = companyRecords.stream()
            .map(CjdwDto::getCORPTYPE)
            .filter(StrUtil::isNotBlank)
            .map(this::mapCorpTypeToEnterpriseType)
            .filter(type -> !"OTHER".equals(type))
            .collect(Collectors.toSet());

        log.info("企业名称: {}", first.getCORPNAME());
        log.info("统一社会信用代码: {}", first.getSOCIALCREDITCODE());
        log.info("原始类型列表: {}", companyRecords.stream().map(CjdwDto::getCORPTYPE).collect(Collectors.toList()));
        log.info("映射后类型Set: {}", enterpriseTypes);
        log.info("Set大小: {}", enterpriseTypes.size());

        if (enterpriseTypes.isEmpty()) {
            aggregated.setCORPTYPE("OTHER");
        } else {
            aggregated.setCORPTYPE(String.join(",", enterpriseTypes));
        }

        return aggregated;
    }

    /**
     * 保存或更新企业信息（同步版本，整合webAdd和audit逻辑）
     *
     * @return 返回部门ID（dept_id）
     */
    private Long saveOrUpdateEnterpriseWithSync(CjdwDto cjdw) {
        // 根据统一社会信用代码查找现有企业
        SysEnterpriseInfo existingEnterprise = sysEnterpriseInfoService.lambdaQuery()
            .eq(SysEnterpriseInfo::getUnifiedSocialCreditCode, cjdw.getSOCIALCREDITCODE())
            .one();

        SysEnterpriseInfo enterprise;
        boolean isNewEnterprise = false;

        if (existingEnterprise != null) {
            // 更新现有企业信息
            enterprise = existingEnterprise;
            updateEnterpriseFromDtoWithMultiTypes(enterprise, cjdw);
            sysEnterpriseInfoService.updateById(enterprise);
        } else {
            // 创建新企业（整合webAdd逻辑）
            enterprise = createEnterpriseFromDtoWithSync(cjdw);

            // 检查部门表是否已存在（参考ins方法逻辑）
            List<SysDept> existingDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptCode, cjdw.getSOCIALCREDITCODE()));

            if (CollUtil.isNotEmpty(existingDepts)) {
                log.warn("企业 {} 对应的部门已存在，返回现有部门ID", cjdw.getCORPNAME());
                // 更新现有部门的类型信息（支持多类型）
                SysDept existingDept = existingDepts.get(0);
                existingDept.setDeptType(cjdw.getCORPTYPE());
                existingDept.setDeptName(cjdw.getCORPNAME());
                sysDeptMapper.updateById(existingDept);
                return existingDept.getDeptId();
            }

            sysEnterpriseInfoService.save(enterprise);
            isNewEnterprise = true;
        }

        // 如果是新企业或企业状态为待审核，执行审核通过逻辑（整合audit逻辑）
        if (isNewEnterprise || "0".equals(enterprise.getEnterpriseStatus())) {
            executeEnterpriseAuditLogic(enterprise);
        }

        // 返回部门ID而不是企业ID
        return enterprise.getDeptId();
    }

    /**
     * 从DTO创建企业实体（同步版本，整合webAdd逻辑）
     */
    private SysEnterpriseInfo createEnterpriseFromDtoWithSync(CjdwDto cjdw) {
        SysEnterpriseInfo enterprise = new SysEnterpriseInfo();
        updateEnterpriseFromDtoWithMultiTypes(enterprise, cjdw);

        // 参考webAdd逻辑设置初始状态
        enterprise.setEnterpriseStatus("0"); // 待审核状态
        enterprise.setDelFlag("0");
        enterprise.setCreateTime(DateUtil.date());

        return enterprise;
    }

    /**
     * 从DTO更新企业信息（支持多类型）
     */
    private void updateEnterpriseFromDtoWithMultiTypes(SysEnterpriseInfo enterprise, CjdwDto cjdw) {
        enterprise.setEnterpriseName(cjdw.getCORPNAME());
        enterprise.setUnifiedSocialCreditCode(cjdw.getSOCIALCREDITCODE());
        enterprise.setLegalRepresentative(cjdw.getLEGALMAN());

        // 设置企业类型（可能是逗号分隔的多类型）
        enterprise.setEnterpriseType(cjdw.getCORPTYPE());

        enterprise.setUpdateTime(DateUtil.date());
    }

    /**
     * 执行企业审核通过逻辑（整合audit接口逻辑）
     */
    private void executeEnterpriseAuditLogic(SysEnterpriseInfo enterprise) {
        try {
            // 创建或更新部门信息
            createOrUpdateDepartment(enterprise);

            // 创建用户信息（如果需要）
            createEnterpriseUser(enterprise);

            // 更新企业状态为审核通过
            enterprise.setEnterpriseStatus("1");
            enterprise.setEnterpriseTime(DateUtil.date());
            sysEnterpriseInfoService.updateById(enterprise);

            log.info("企业审核通过处理完成：{}", enterprise.getEnterpriseName());

        } catch (Exception e) {
            log.error("企业审核处理失败：{}", enterprise.getEnterpriseName(), e);
            throw new RuntimeException("企业审核处理失败：" + e.getMessage());
        }
    }

    /**
     * 创建或更新部门信息（整合audit接口中的部门创建逻辑）
     */
    private void createOrUpdateDepartment(SysEnterpriseInfo enterprise) {
        // 检查部门是否已存在
        List<SysDept> existingDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptCode, enterprise.getUnifiedSocialCreditCode()));

        if (CollUtil.isEmpty(existingDepts)) {
            // 创建新部门
            SysDept dept = new SysDept();
            dept.setParentId(200L);
            dept.setAncestors("0,200");
            dept.setDeptName(enterprise.getEnterpriseName());
            dept.setDeptType(enterprise.getEnterpriseType()); // 支持逗号分隔的多类型
            dept.setDeptCode(enterprise.getUnifiedSocialCreditCode());
            dept.setDeptCategory(enterprise.getUnifiedSocialCreditCode());
            dept.setProvinceCode(enterprise.getRegistrationRegionProvince());
            dept.setCityCode(enterprise.getRegistrationRegionCity());
            dept.setDistrictCode(enterprise.getRegistrationRegionArea());
            dept.setOrderNum(1);
            dept.setStatus("0");
            dept.setDelFlag("0");
            dept.setCreateTime(DateUtil.date());

            int result = sysDeptMapper.insert(dept);
            if (result == 1) {
                enterprise.setDeptId(dept.getDeptId());
                log.info("创建部门成功：{} (类型: {})", dept.getDeptName(), dept.getDeptType());
            } else {
                throw new RuntimeException("创建部门失败");
            }
        } else {
            // 更新现有部门的类型信息（支持多类型）
            SysDept existingDept = existingDepts.get(0);
            existingDept.setDeptType(enterprise.getEnterpriseType());
            existingDept.setDeptName(enterprise.getEnterpriseName());
            sysDeptMapper.updateById(existingDept);
            enterprise.setDeptId(existingDept.getDeptId());
            log.info("更新部门成功：{} (类型: {})", existingDept.getDeptName(), existingDept.getDeptType());
        }
    }

    /**
     * 创建企业用户（整合audit接口中的用户创建逻辑）
     */
    private void createEnterpriseUser(SysEnterpriseInfo enterprise) {
        // 检查用户是否已存在
        SysUserBo userCheck = new SysUserBo();
        userCheck.setUserName(enterprise.getUnifiedSocialCreditCode());

        if (userService.checkUserNameUnique(userCheck)) {
            // 创建新用户
            SysUserBo user = new SysUserBo();
            user.setDeptId(enterprise.getDeptId());
            user.setUserName(enterprise.getUnifiedSocialCreditCode());
            user.setNickName(enterprise.getEnterpriseName());
            user.setUserType("sys_user");
            user.setPhonenumber(enterprise.getOfficePhone());
            user.setPassword(enterprise.getOfficePhone() != null ? enterprise.getOfficePhone() : "123456");
            user.setStatus("0");

            // 根据企业类型分配角色（支持多类型）
            user.setRoleIds(getRoleIdsByEnterpriseTypes(enterprise.getEnterpriseType()));

            user.setPassword(BCrypt.hashpw(user.getPassword()));
            // 绕过数据权限检查，避免施工方用户权限限制导致的角色访问失败
            int result = DataPermissionHelper.ignore(() -> userService.insertUser(user));
            if (result > 0) {
                enterprise.setUserId(user.getUserId());
                log.info("创建用户成功：{} (角色: {})", user.getUserName(),
                    Arrays.toString(user.getRoleIds()));
            }
        } else {
            log.info("用户已存在，跳过创建：{}", enterprise.getUnifiedSocialCreditCode());
        }
    }

    /**
     * 根据企业类型获取角色ID数组（支持多类型）
     */
    private Long[] getRoleIdsByEnterpriseTypes(String enterpriseTypes) {
        if (StrUtil.isBlank(enterpriseTypes)) {
            return new Long[0];
        }

        Set<Long> roleIds = new HashSet<>();
        String[] types = enterpriseTypes.split(",");

        for (String type : types) {
            type = type.trim();
            try {
                // 绕过数据权限检查，确保能够查询到所有需要的角色
//                String finalType = type;
//                SysRole role = DataPermissionHelper.ignore(() ->
//                    roleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
//                        .eq(SysRole::getRoleKey, finalType)));
                SysRole role = roleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
                    .eq(SysRole::getRoleKey, type));
                if (role != null) {
                    roleIds.add(role.getRoleId());
                }
            } catch (Exception e) {
                log.warn("获取角色失败：{}", type, e);
            }
        }

        return roleIds.toArray(new Long[0]);
    }

    /**
     * 映射企业类型
     */
    private String mapCorpTypeToEnterpriseType(String corpType) {
        return switch (corpType) {
            case "建设单位" -> "CLIENT";
            case "施工单位" -> "CONSTRUCTION";
            case "监理单位" -> "SUPERVISION";
            case "设计单位" -> "DESIGN";
            case "勘察单位" -> "SURVEY";
            case "安拆单位" -> "INSTALLATION_DISMANTLING";
            case "维保单位" -> "MAINTENANCE";
            case "使用单位" -> "USER";
            default -> "OTHER";
        };
    }

    /**
     * 更新项目的单位关联信息（设置部门ID）
     */
    private void updateProjectCompanyReferences(PrjProjectsVo projectVo, Map<String, Long> companyDeptIdMap) {
        // 设置五方单位的部门ID（逻辑外键至 sys_dept.dept_id）
        companyDeptIdMap.forEach((corpType, deptId) -> {
            switch (corpType) {
                case "建设单位" -> projectVo.setClientOrgId(deptId);
                case "施工单位" -> projectVo.setConstructionOrgId(deptId);
                case "监理单位" -> projectVo.setSupervisionOrgId(deptId);
                case "设计单位" -> projectVo.setDesignOrgId(deptId);
                case "勘察单位" -> projectVo.setSurveyOrgId(deptId);
                case "安拆单位" -> projectVo.setInstallationDismantlingOrgId(deptId);
                case "维保单位" -> projectVo.setMaintenanceOrgId(deptId);
            }
        });
    }

    /**
     * 处理人员信息
     *
     * @param projectVo        项目信息
     * @param xgryList         外部API返回的人员信息列表
     * @param companyDeptIdMap 企业类型到部门ID的映射
     * @return 人员ID到角色的映射，用于后续绑定项目人员关联
     */
    private Map<Long, String> processPersonnelInfo(PrjProjectsVo projectVo, java.util.List<XgryDto> xgryList, Map<String, Long> companyDeptIdMap) {
        Map<Long, String> personnelRoleMap = new HashMap<>();

        if (CollUtil.isEmpty(xgryList)) {
            log.info("项目 {} 没有人员信息需要同步", projectVo.getProjectName());
            return personnelRoleMap;
        }

        // 过滤出与当前项目相关的人员
        List<XgryDto> relevantPersonnel = xgryList.stream()
            .filter(x -> projectVo.getConstructionPermitNo().equals(x.getCONSTRUCTIONPERMITNUM()))
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(relevantPersonnel)) {
            log.info("项目 {} 没有匹配的人员信息", projectVo.getProjectName());
            return personnelRoleMap;
        }

        log.info("项目 {} 开始处理 {} 名相关人员", projectVo.getProjectName(), relevantPersonnel.size());

        for (XgryDto xgry : relevantPersonnel) {
            try {
                // 处理单个人员信息
                Long personId = processIndividualPersonnel(xgry, companyDeptIdMap);
                if (personId != null) {
                    // 映射人员类型到项目角色
                    String roleOnProject = mapPersonTypeToProjectRole(xgry.getPERSONTYPE());
                    personnelRoleMap.put(personId, roleOnProject);
                    log.debug("成功处理人员：{} (ID: {}, 角色: {})", xgry.getNAME(), personId, roleOnProject);
                }
            } catch (Exception e) {
                log.error("处理人员信息失败：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD(), e);
            }
        }

        log.info("项目 {} 成功处理 {} 名人员信息", projectVo.getProjectName(), personnelRoleMap.size());
        return personnelRoleMap;
    }

    /**
     * 处理单个人员信息
     *
     * @param xgry             外部API返回的人员信息
     * @param companyDeptIdMap 企业类型到部门ID的映射
     * @return 人员ID，如果处理失败返回null
     */
    private Long processIndividualPersonnel(XgryDto xgry, Map<String, Long> companyDeptIdMap) {
        try {
            // 1. 验证必要字段
            if (StrUtil.isBlank(xgry.getNAME()) || StrUtil.isBlank(xgry.getIDENTITYCARD())) {
                log.warn("人员信息缺少必要字段：姓名={}, 身份证={}", xgry.getNAME(), xgry.getIDENTITYCARD());
                return null;
            }

            // 2. 根据身份证号查找现有人员
            SysPersonVo existingPerson = sysPersonService.queryByIdCard(xgry.getIDENTITYCARD());

            if (existingPerson != null) {
                // 更新现有人员信息
                updatePersonFromXgry(existingPerson, xgry, companyDeptIdMap);
                return existingPerson.getPersonId();
            } else {
                // 创建新人员
                return createPersonFromXgry(xgry, companyDeptIdMap);
            }

        } catch (Exception e) {
            log.error("处理人员信息失败：{}", xgry.getNAME(), e);
            return null;
        }
    }

    /**
     * 从外部API数据创建新人员
     */
    private Long createPersonFromXgry(XgryDto xgry, Map<String, Long> companyDeptIdMap) {
        try {
            SysPersonBo personBo = new SysPersonBo();

            // 设置基本信息
            personBo.setName(xgry.getNAME());
            personBo.setIdCard(xgry.getIDENTITYCARD());
            personBo.setPhone(xgry.getLXDH());

            // 从身份证号码中提取性别信息
            String gender = extractGenderFromIdCard(xgry.getIDENTITYCARD());
            if (StrUtil.isNotBlank(gender)) {
                personBo.setGender(gender);
                log.debug("从身份证号码提取性别：{} -> {}", xgry.getIDENTITYCARD(), gender);
            } else {
                log.warn("无法从身份证号码提取性别：{}", xgry.getIDENTITYCARD());
            }

            // 根据企业名称查找企业ID
            Long enterpriseId = findEnterpriseIdByName(xgry.getCORPNAME(), companyDeptIdMap);
            if (enterpriseId != null) {
                personBo.setEnterpriseId(enterpriseId);
            } else {
                log.warn("未找到企业信息：{}", xgry.getCORPNAME());
            }

            // 保存人员信息
            boolean success = sysPersonService.insertByBo(personBo);
            if (success) {
                log.info("创建人员成功：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD());
                return personBo.getPersonId();
            } else {
                log.error("创建人员失败：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD());
                return null;
            }

        } catch (Exception e) {
            log.error("创建人员失败：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD(), e);
            return null;
        }
    }

    /**
     * 更新现有人员信息
     */
    private void updatePersonFromXgry(SysPersonVo existingPerson, XgryDto xgry, Map<String, Long> companyDeptIdMap) {
        try {
            SysPersonBo personBo = new SysPersonBo();
            personBo.setPersonId(existingPerson.getPersonId());
            personBo.setName(xgry.getNAME());
            personBo.setIdCard(xgry.getIDENTITYCARD());

            // 更新手机号（如果有）
            if (StrUtil.isNotBlank(xgry.getLXDH())) {
                personBo.setPhone(xgry.getLXDH());
            } else {
                personBo.setPhone(existingPerson.getPhone());
            }

            // 从身份证号码中提取性别信息（如果现有记录没有性别或性别为空）
            if (StrUtil.isBlank(existingPerson.getGender())) {
                String gender = extractGenderFromIdCard(xgry.getIDENTITYCARD());
                if (StrUtil.isNotBlank(gender)) {
                    personBo.setGender(gender);
                    log.debug("为现有人员补充性别信息：{} -> {}", xgry.getIDENTITYCARD(), gender);
                } else {
                    personBo.setGender(existingPerson.getGender());
                }
            } else {
                personBo.setGender(existingPerson.getGender());
            }

            // 更新企业关联（如果有变化）
            Long newEnterpriseId = findEnterpriseIdByName(xgry.getCORPNAME(), companyDeptIdMap);
            if (newEnterpriseId != null && !newEnterpriseId.equals(existingPerson.getEnterpriseId())) {
                personBo.setEnterpriseId(newEnterpriseId);
                log.info("更新人员企业关联：{} - {} -> {}", xgry.getNAME(), existingPerson.getEnterpriseId(), newEnterpriseId);
            } else {
                personBo.setEnterpriseId(existingPerson.getEnterpriseId());
            }

            // 保存更新
            boolean success = sysPersonService.updateByBo(personBo);
            if (success) {
                log.debug("更新人员信息成功：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD());
            } else {
                log.warn("更新人员信息失败：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD());
            }

        } catch (Exception e) {
            log.error("更新人员信息失败：{} - {}", xgry.getNAME(), xgry.getIDENTITYCARD(), e);
        }
    }

    /**
     * 根据企业名称查找企业ID
     */
    private Long findEnterpriseIdByName(String corpName, Map<String, Long> companyDeptIdMap) {
        if (StrUtil.isBlank(corpName)) {
            return null;
        }

        try {
            // 根据企业名称查找企业信息
            SysEnterpriseInfo enterprise = sysEnterpriseInfoService.lambdaQuery()
                .eq(SysEnterpriseInfo::getEnterpriseName, corpName)
                .one();

            if (enterprise != null) {
                return enterprise.getEnterpriseId();
            }

            log.warn("未找到企业：{}", corpName);
            return null;

        } catch (Exception e) {
            log.error("查找企业失败：{}", corpName, e);
            return null;
        }
    }

    /**
     * 映射人员类型到项目角色
     */
    private String mapPersonTypeToProjectRole(String personType) {
        if (StrUtil.isBlank(personType)) {
            return "OTHER";
        }

        try {
            // 从字典中获取映射关系
            String dictValue = dictService.getDictValue("personnel_position", personType);
            if (StrUtil.isNotBlank(dictValue)) {
                return dictValue;
            } else {
                log.warn("未找到人员类型 '{}' 对应的字典映射，使用默认值 'OTHER'", personType);
                return "OTHER";
            }
        } catch (Exception e) {
            log.error("从字典获取人员类型映射失败：{}", personType, e);
            return "OTHER";
        }
    }

    /**
     * 绑定项目人员关联（同步版本）
     *
     * @param projectId        项目ID
     * @param personnelRoleMap 人员ID到角色的映射
     */
    private void bindProjectPersonnelFromSync(Long projectId, Map<Long, String> personnelRoleMap) {
        if (projectId == null || CollUtil.isEmpty(personnelRoleMap)) {
            log.info("项目 {} 没有人员需要绑定", projectId);
            return;
        }

        log.info("项目 {} 开始绑定 {} 名人员", projectId, personnelRoleMap.size());

        // 查询项目现有人员及其角色
        PrjPersonnelBo queryBo = new PrjPersonnelBo();
        queryBo.setProjectId(projectId);
        List<PrjPersonnelVo> existingPersonnel = prjPersonnelService.queryList(queryBo);

        int successCount = 0;
        for (Map.Entry<Long, String> entry : personnelRoleMap.entrySet()) {
            Long personId = entry.getKey();
            String roleOnProject = entry.getValue();

            try {
                // 检查该人员在此项目下是否已有相同角色
                boolean hasDuplicateRole = existingPersonnel.stream()
                    .anyMatch(p -> p.getPersonId().equals(personId) && p.getRoleOnProject().equals(roleOnProject));

                if (hasDuplicateRole) {
                    log.debug("人员 {} 在项目 {} 中已存在角色 {}，跳过", personId, projectId, roleOnProject);
                    continue;
                }

                // 获取人员信息以确定所属企业
                SysPersonVo personVo = sysPersonService.queryById(personId);
                if (personVo == null) {
                    log.warn("未找到人员信息：{}", personId);
                    continue;
                }

                // 创建项目人员关联
                PrjPersonnelBo personnelBo = new PrjPersonnelBo();
                personnelBo.setProjectId(projectId);
                personnelBo.setPersonId(personId);
                personnelBo.setRoleOnProject(roleOnProject);

                // 根据企业ID查找对应的部门ID作为orgId
                if (personVo.getEnterpriseId() != null) {
                    SysEnterpriseInfo enterprise = sysEnterpriseInfoService.getById(personVo.getEnterpriseId());
                    if (enterprise != null && enterprise.getDeptId() != null) {
                        personnelBo.setOrgId(enterprise.getDeptId());
                    }
                }

                boolean success = prjPersonnelService.insertByBo(personnelBo);
                if (success) {
                    successCount++;
                    log.debug("绑定人员成功：项目={}, 人员={}, 角色={}", projectId, personId, roleOnProject);
                } else {
                    log.warn("绑定人员失败：项目={}, 人员={}, 角色={}", projectId, personId, roleOnProject);
                }

            } catch (Exception e) {
                log.error("绑定人员异常：项目={}, 人员={}, 角色={}", projectId, personId, roleOnProject, e);
            }
        }

        log.info("项目 {} 成功绑定 {} 名人员", projectId, successCount);
    }

    /**
     * 处理设备信息
     */
    private void processEquipmentInfo(PrjProjectsVo projectVo, java.util.List<QzjxDto> qzjxList) {
        if (CollUtil.isEmpty(qzjxList)) {
            return;
        }

        // 这里可以根据需要处理设备信息
        // 由于设备信息较复杂，暂时记录数量
        long equipmentCount = qzjxList.stream()
            .filter(x -> projectVo.getConstructionPermitNo().equals(x.getCONSTRUCTIONPERMITNUM()))
            .count();

        log.info("项目 {} 同步到 {} 台设备", projectVo.getProjectName(), equipmentCount);
    }

    /**
     * 保存或更新项目信息
     */
    private void saveOrUpdateProject(PrjProjectsVo projectVo) {
        try {
            // 根据施工许可证编号查找现有项目
            PrjProjectsBo queryBo = new PrjProjectsBo();
            queryBo.setConstructionPermitNo(projectVo.getConstructionPermitNo());
            List<PrjProjectsVo> existingProjects = prjProjectsService.queryList(queryBo);

            // 手动创建 PrjProjectsBo 对象并复制属性
            PrjProjectsBo projectBo = createProjectBoFromVo(projectVo);

            if (CollUtil.isNotEmpty(existingProjects)) {
                // 更新现有项目
                PrjProjectsVo existingProject = existingProjects.get(0);
                projectBo.setProjectId(existingProject.getProjectId());
                prjProjectsService.updateByBo(projectBo);
                projectVo.setProjectId(existingProject.getProjectId());
                log.info("更新项目信息成功：{}", projectVo.getProjectName());
            } else {
                // 创建新项目
                prjProjectsService.insertByBo(projectBo);
                projectVo.setProjectId(projectBo.getProjectId());
                log.info("创建项目信息成功：{}", projectVo.getProjectName());
            }
        } catch (Exception e) {
            log.error("保存项目信息失败：{}", projectVo.getProjectName(), e);
            throw new RuntimeException("保存项目信息失败：" + e.getMessage());
        }
    }

    /**
     * 从 PrjProjectsVo 创建 PrjProjectsBo 对象
     */
    private PrjProjectsBo createProjectBoFromVo(PrjProjectsVo projectVo) {
        PrjProjectsBo projectBo = new PrjProjectsBo();

        // 使用 Hutool BeanUtil 复制属性，忽略不匹配的属性
        BeanUtil.copyProperties(projectVo, projectBo, true);

        return projectBo;
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        try {
            if (StrUtil.isBlank(dateStr)) {
                return null;
            }
            // 处理常见的日期格式
            if (dateStr.contains(" ")) {
                return DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
            } else {
                return DateUtils.parseDate(dateStr, "yyyy-MM-dd");
            }
        } catch (Exception e) {
            log.warn("日期解析失败：{}", dateStr, e);
            return null;
        }
    }

    /**
     * 从身份证号码中提取性别信息
     *
     * @param idCard 身份证号码
     * @return 性别代码（根据sys_user_sex字典：0-男性，1-女性，2-未知），如果提取失败返回null
     */
    private String extractGenderFromIdCard(String idCard) {
        try {
            if (StrUtil.isBlank(idCard)) {
                return null;
            }

            if (!IdcardUtil.isValidCard(idCard)) {
                log.warn("身份证号码格式不正确：{}", idCard);
                return null;
            }

            int hutoolGender = IdcardUtil.getGenderByIdCard(idCard);

            // 转换为系统字典值（sys_user_sex）：0-男性，1-女性
            if (hutoolGender == 1) {
                return "0"; // 男性
            } else if (hutoolGender == 0) {
                return "1"; // 女性
            } else {
                return "2"; // 未知
            }

        } catch (Exception e) {
            log.error("从身份证号码提取性别失败：{}", idCard, e);
            return null;
        }
    }
}
